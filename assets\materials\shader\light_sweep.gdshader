shader_type canvas_item;

// 控制参数
uniform float sweep_speed : hint_range(0.0, 20.0) = 9.0; // 扫光速度
uniform float wave_frequency : hint_range(0.0, 10.0) = 3.0; // 波浪频率
uniform float intensity : hint_range(0.0, 2.0) = 0.9; // 扫光强度
uniform float brightness : hint_range(0.0, 1.0) = 0.6; // 亮度倍数
uniform vec4 sweep_color : source_color = vec4(1.0, 1.0, 1.0, 1.0); // 扫光颜色
uniform vec2 direction = vec2(1.0, 1.0); // 扫光方向 (x, y)

void vertex() {
	// Called for every vertex the material is visible on.
}

void fragment() {
	// 获取标准化的UV坐标 (相当于 fragCoord.xy / iResolution.xy)
	vec2 uv = UV;

	// 计算扫光效果，使用方向参数控制扫光方向
	float sweep = sin(uv.y * direction.y + uv.x * direction.x * wave_frequency - TIME * sweep_speed) * intensity;
	sweep *= sweep * sweep * brightness;

	// 限制扫光值在0到1之间
	sweep = clamp(sweep, 0.0, 1.0);

	// 获取原纹理颜色 (使用TEXTURE而不是额外的uniform纹理)
	vec4 base_color = texture(TEXTURE, uv);


	// 最终颜色 = 原纹理 + 扫光效果
	COLOR = base_color + vec4(sweep * sweep_color.rgb, 0.0);
}
