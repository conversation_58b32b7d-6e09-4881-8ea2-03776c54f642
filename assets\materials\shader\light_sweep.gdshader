shader_type canvas_item;

// 控制参数
uniform float sweep_speed : hint_range(0.0, 20.0) = 9.0; // 扫光速度
uniform float wave_frequency : hint_range(0.0, 10.0) = 3.0; // 波浪频率
uniform float intensity : hint_range(0.0, 2.0) = 0.9; // 扫光强度
uniform float brightness : hint_range(0.0, 1.0) = 0.6; // 亮度倍数
uniform vec4 sweep_color : source_color = vec4(1.0, 1.0, 1.0, 1.0); // 扫光颜色
uniform sampler2D base_texture : source_color; // 基础纹理

void vertex() {
	// Called for every vertex the material is visible on.
}

void fragment() {
	// 获取标准化的UV坐标 (相当于 fragCoord.xy / iResolution.xy)
	vec2 uv = UV;

	// 计算扫光效果 (相当于原shader中的sin计算)
	float sweep = sin(uv.y + uv.x * wave_frequency - TIME * sweep_speed) * intensity;
	sweep *= sweep * sweep * brightness;

	// 限制扫光值在0到1之间
	sweep = clamp(sweep, 0.0, 1.0);

	// 获取基础纹理颜色 (相当于 texture(iChannel0, r))
	vec4 base_color = texture(base_texture, uv);

	// 最终颜色 = 基础纹理 + 扫光效果
	COLOR = base_color + vec4(sweep * sweep_color.rgb, 0.0);
}
