class_name RelicUIData
extends UIItemDataBase

## 遗物UI数据类
##
## 用于在升级面板中显示遗物信息的数据结构
## 包含遗物的名称、描述、图标、效果等UI显示所需的所有信息

## 遗物名称
var name: String = ""

## 遗物描述
var description: String = ""

var icon: Texture2D = null

var level: int = 1

## 遗物稀有度
var rarity_level: GameConstants.RarityLevel = GameConstants.RarityLevel.COMMON

## 构造函数
## @param p_name: 遗物名称
## @param p_description: 遗物描述
## @param p_icon: 遗物图标
## @param p_effects: 效果描述列表
## @param p_rarity: 遗物稀有度
func _init(
	p_name: String = "",
	p_description: String = "",
	p_icon = "", # 可以是String或Texture2D
	p_rarity: GameConstants.RarityLevel = GameConstants.RarityLevel.COMMON,
	p_level: int = 1
):
	name = p_name
	description = p_description
	icon = p_icon
	rarity_level = p_rarity
	level = p_level


## 获取格式化的遗物信息字符串
## @return: 格式化的信息字符串
func get_formatted_info() -> String:
	var info_parts: Array[String] = []

	info_parts.append("名称: " + name)
	info_parts.append("稀有度: " + get_rarity_display_name())
	info_parts.append("描述: " + description)

	return "\n".join(info_parts)

## 检查数据是否有效
## @return: 数据是否完整有效
func is_valid() -> bool:
	return name != "" and icon != null

## 获取稀有度的显示名称
## @return: 稀有度的中文显示名称
func get_rarity_display_name() -> String:
	match rarity_level:
		"common":
			return "普通"
		"uncommon":
			return "稀有"
		"rare":
			return "史诗"
		"epic":
			return "传说"
		"legendary":
			return "神话"
		_:
			return "未知"

## 获取稀有度对应的颜色
## @return: 稀有度对应的颜色
func get_rarity_color() -> Color:
	match rarity_level:
		"common":
			return Color.WHITE
		"uncommon":
			return Color.GREEN
		"rare":
			return Color.BLUE
		"epic":
			return Color.PURPLE
		"legendary":
			return Color.ORANGE
		_:
			return Color.GRAY


## 获取简短的显示名称（用于UI显示）
## @return: 简短的显示名称
func get_display_name() -> String:
	return name

## 创建数据的副本
## @return: 新的RelicUIData实例
func create_copy() -> RelicUIData:
	var new_data = RelicUIData.new()
	new_data.name = name
	new_data.description = description
	new_data.icon = icon
	new_data.rarity_level = rarity_level
	return new_data
