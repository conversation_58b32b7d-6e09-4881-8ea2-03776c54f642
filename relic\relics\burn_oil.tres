[gd_resource type="Resource" script_class="Relic" load_steps=19 format=3 uid="uid://pstjyta8m8p"]

[ext_resource type="Script" uid="uid://crf0ktn1ipx6t" path="res://relic/Relic.gd" id="1_78uto"]
[ext_resource type="Script" uid="uid://rpolio4qr86r" path="res://relic/effects/BuffConditionalDamageRelicEffect.gd" id="1_ihp66"]
[ext_resource type="Script" uid="uid://coux00joxi7h1" path="res://addons/attribute_manager/Attribute.gd" id="1_vcttg"]
[ext_resource type="Script" uid="uid://doe3ryr4echpc" path="res://attribute/range_random_attribute.gd" id="2_1csi7"]
[ext_resource type="Script" uid="uid://2dxckbgqoga5" path="res://addons/attribute_manager/AttributeSet.gd" id="2_64nij"]
[ext_resource type="Texture2D" uid="uid://donaevmmjeptc" path="res://assets/imgs/balls/普通球.png" id="2_cae7g"]
[ext_resource type="Script" uid="uid://h4qchw2pf3g7" path="res://addons/attribute_manager/AttributeBuffCount.gd" id="3_1csi7"]
[ext_resource type="Script" uid="uid://5ww2mn76prh4" path="res://attribute/leveled_attribute.gd" id="3_q13pw"]

[sub_resource type="Resource" id="Resource_6vfjg"]
script = ExtResource("1_vcttg")
base_value = 0.0
can_cache = true

[sub_resource type="Resource" id="Resource_arwwp"]
script = ExtResource("1_vcttg")
base_value = 1.0
can_cache = true
metadata/_custom_type_script = "uid://coux00joxi7h1"

[sub_resource type="Resource" id="Resource_gbnye"]
script = ExtResource("1_vcttg")
base_value = 2.0
can_cache = true

[sub_resource type="Resource" id="Resource_irxy4"]
script = ExtResource("1_vcttg")
base_value = 1.0
can_cache = true

[sub_resource type="Resource" id="Resource_nshgp"]
script = ExtResource("3_q13pw")
growth_per_level = 10.0
base_value = 20.0
can_cache = true
metadata/_custom_type_script = "uid://5ww2mn76prh4"

[sub_resource type="Resource" id="Resource_e3g7s"]
script = ExtResource("3_q13pw")
growth_per_level = 5.0
base_value = 10.0
can_cache = true
metadata/_custom_type_script = "uid://5ww2mn76prh4"

[sub_resource type="Resource" id="Resource_hdyti"]
script = ExtResource("2_1csi7")
min_value_source = "min_value"
max_value_source = "max_value"
base_value = 0.0
can_cache = false
metadata/_custom_type_script = "uid://doe3ryr4echpc"

[sub_resource type="Resource" id="Resource_ouofm"]
script = ExtResource("2_64nij")
attributes = Dictionary[StringName, ExtResource("1_vcttg")]({
&"duration": SubResource("Resource_6vfjg"),
&"level": SubResource("Resource_arwwp"),
&"max_counts": SubResource("Resource_gbnye"),
&"max_stacks": SubResource("Resource_irxy4"),
&"max_value": SubResource("Resource_nshgp"),
&"min_value": SubResource("Resource_e3g7s"),
&"value": SubResource("Resource_hdyti")
})

[sub_resource type="Resource" id="Resource_1ppye"]
script = ExtResource("3_1csi7")
buff_name = "damage"
operation = 0
policy = 0
merging = 2
attribute_set = SubResource("Resource_ouofm")
metadata/_custom_type_script = "uid://h4qchw2pf3g7"

[sub_resource type="Resource" id="Resource_k7bwc"]
script = ExtResource("1_ihp66")
buff_to_apply = SubResource("Resource_1ppye")
required_enemy_buff_name = &"burn"
ball_target_attribute = &"damage"
metadata/_custom_type_script = "uid://rpolio4qr86r"

[resource]
script = ExtResource("1_78uto")
name = "油壶"
description = "弹球击中燃烧敌人时，下次碰撞伤害增加"
icon = ExtResource("2_cae7g")
effect = SubResource("Resource_k7bwc")
rarity_level = 0
rarity_weight = 8.0
metadata/_custom_type_script = "uid://crf0ktn1ipx6t"
