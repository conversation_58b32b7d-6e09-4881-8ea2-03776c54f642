class_name UpgradeOptionGenerator
extends RefCounted

## 升级选项生成器
##
## 负责生成升级选项，包括稀有度系统、权重配置和批量调整功能
## 支持弹球和遗物的统一稀有度管理

## 使用全局常量中的稀有度枚举和物品类型枚举

## 物品权重配置结构
class ItemWeightConfig:
	var item_id: String = "" ## 物品唯一标识符
	var rarity: GameConstants.RarityLevel = GameConstants.RarityLevel.COMMON ## 稀有度等级
	var weight: float = 1.0 ## 权重值（必须>=0）
	var item_type: GameConstants.ItemType = GameConstants.ItemType.BALL ## 物品类型
	var item_resource = null ## 物品资源引用
	var ui_data: UIItemDataBase = null ## 完整的UI数据引用

	func _init(p_item_id: String = "", p_rarity: GameConstants.RarityLevel = GameConstants.RarityLevel.COMMON, p_weight: float = 1.0, p_item_type: GameConstants.ItemType = GameConstants.ItemType.BALL):
		item_id = p_item_id
		rarity = p_rarity
		weight = max(0.0, p_weight) # 确保权重>=0
		item_type = p_item_type

	## 验证配置是否有效
	func is_valid() -> bool:
		return item_id != "" and weight >= 0.0 and GameConstants.is_valid_item_type(item_type)

## 权重配置存储
var _weight_configs: Dictionary = {} # item_id -> ItemWeightConfig

## 添加或更新物品权重配置
## @param item_id: 物品唯一标识符
## @param rarity: 稀有度等级
## @param weight: 权重值（必须>=0）
## @param item_type: 物品类型枚举
## @param item_resource: 物品资源引用（可选）
## @param ui_data: 完整的UI数据引用（可选）
func set_item_weight_config(item_id: String, rarity: GameConstants.RarityLevel, weight: float, item_type: GameConstants.ItemType, item_resource = null, ui_data: UIItemDataBase = null) -> void:
	if item_id == "":
		push_error("UpgradeOptionGenerator: 物品ID不能为空")
		return

	if weight < 0.0:
		push_warning("UpgradeOptionGenerator: 权重值不能小于0，已自动调整为0")
		weight = 0.0

	var config = ItemWeightConfig.new(item_id, rarity, weight, item_type)
	config.item_resource = item_resource
	config.ui_data = ui_data
	_weight_configs[item_id] = config

	print("UpgradeOptionGenerator: 设置物品权重配置 - ID: %s, 稀有度: %s, 权重: %.2f, 类型: %s" % [item_id, GameConstants.get_rarity_display_name(rarity), weight, GameConstants.get_item_type_name(item_type)])

## 获取物品权重配置
## @param item_id: 物品唯一标识符
## @return: ItemWeightConfig实例，如果不存在返回null
func get_item_weight_config(item_id: String) -> ItemWeightConfig:
	return _weight_configs.get(item_id, null)

## 获取所有权重配置
## @return: 所有ItemWeightConfig实例的数组
func get_all_weight_configs() -> Array[ItemWeightConfig]:
	var configs: Array[ItemWeightConfig] = []
	for config in _weight_configs.values():
		configs.append(config)
	return configs

## 按稀有度筛选物品配置
## @param rarity: 稀有度等级
## @return: 指定稀有度的ItemWeightConfig数组
func get_configs_by_rarity(rarity: GameConstants.RarityLevel) -> Array[ItemWeightConfig]:
	var filtered_configs: Array[ItemWeightConfig] = []
	for config in _weight_configs.values():
		if config.rarity == rarity:
			filtered_configs.append(config)
	return filtered_configs

## 按物品类型筛选物品配置
## @param item_type: 物品类型枚举
## @return: 指定类型的ItemWeightConfig数组
func get_configs_by_type(item_type: GameConstants.ItemType) -> Array[ItemWeightConfig]:
	var filtered_configs: Array[ItemWeightConfig] = []
	for config in _weight_configs.values():
		if config.item_type == item_type:
			filtered_configs.append(config)
	return filtered_configs

## 批量调整指定稀有度的物品权重（加法）
## @param rarity: 稀有度等级
## @param add_value: 要增加的权重值
## @return: 受影响的物品数量
func adjust_rarity_weights_add(rarity: GameConstants.RarityLevel, add_value: float) -> int:
	var affected_count: int = 0
	var configs = get_configs_by_rarity(rarity)

	for config in configs:
		var new_weight = max(0.0, config.weight + add_value) # 确保权重>=0
		config.weight = new_weight
		affected_count += 1

	print("UpgradeOptionGenerator: 批量调整稀有度权重（加法） - 稀有度: %s, 增加值: %.2f, 影响物品数: %d" % [GameConstants.get_rarity_display_name(rarity), add_value, affected_count])
	return affected_count

## 批量调整指定稀有度的物品权重（乘法）
## @param rarity: 稀有度等级
## @param multiplier: 权重倍数（必须>=0）
## @return: 受影响的物品数量
func adjust_rarity_weights_multiply(rarity: GameConstants.RarityLevel, multiplier: float) -> int:
	if multiplier < 0.0:
		push_warning("UpgradeOptionGenerator: 权重倍数不能小于0，已自动调整为0")
		multiplier = 0.0

	var affected_count: int = 0
	var configs = get_configs_by_rarity(rarity)

	for config in configs:
		config.weight = config.weight * multiplier
		affected_count += 1

	print("UpgradeOptionGenerator: 批量调整稀有度权重（乘法） - 稀有度: %s, 倍数: %.2f, 影响物品数: %d" % [GameConstants.get_rarity_display_name(rarity), multiplier, affected_count])
	return affected_count

## 批量设置指定稀有度的物品权重为固定值
## @param rarity: 稀有度等级
## @param new_weight: 新的权重值（必须>=0）
## @return: 受影响的物品数量
func set_rarity_weights(rarity: GameConstants.RarityLevel, new_weight: float) -> int:
	if new_weight < 0.0:
		push_warning("UpgradeOptionGenerator: 权重值不能小于0，已自动调整为0")
		new_weight = 0.0

	var affected_count: int = 0
	var configs = get_configs_by_rarity(rarity)

	for config in configs:
		config.weight = new_weight
		affected_count += 1

	print("UpgradeOptionGenerator: 批量设置稀有度权重 - 稀有度: %s, 新权重: %.2f, 影响物品数: %d" % [GameConstants.get_rarity_display_name(rarity), new_weight, affected_count])
	return affected_count

## 移除物品权重配置
## @param item_id: 物品唯一标识符
## @return: 是否成功移除
func remove_item_weight_config(item_id: String) -> bool:
	if _weight_configs.has(item_id):
		_weight_configs.erase(item_id)
		print("UpgradeOptionGenerator: 移除物品权重配置 - ID: %s" % item_id)
		return true
	return false

## 清空所有权重配置
func clear_all_weight_configs() -> void:
	var count = _weight_configs.size()
	_weight_configs.clear()
	print("UpgradeOptionGenerator: 清空所有权重配置，共移除 %d 个配置" % count)

## 获取权重配置统计信息
## @return: 包含统计信息的字典
func get_weight_config_stats() -> Dictionary:
	var stats = {
		"total_count": _weight_configs.size(),
		"rarity_counts": {},
		"type_counts": {},
		"total_weight": 0.0
	}

	# 初始化稀有度计数
	for rarity in GameConstants.RarityLevel.values():
		stats.rarity_counts[rarity] = 0

	# 统计各项数据
	for config in _weight_configs.values():
		stats.rarity_counts[config.rarity] += 1
		stats.type_counts[config.item_type] = stats.type_counts.get(config.item_type, 0) + 1
		stats.total_weight += config.weight

	return stats

## 验证所有权重配置的有效性
## @return: 无效配置的item_id数组
func validate_all_configs() -> Array[String]:
	var invalid_ids: Array[String] = []

	for item_id in _weight_configs.keys():
		var config = _weight_configs[item_id]
		if not config.is_valid():
			invalid_ids.append(item_id)

	if invalid_ids.size() > 0:
		push_warning("UpgradeOptionGenerator: 发现 %d 个无效的权重配置" % invalid_ids.size())

	return invalid_ids

## 从弹球资源自动提取稀有度信息
## @param ball_resource: BallUIResource实例
## @return: 对应的稀有度等级
static func extract_ball_rarity(ball_resource: BallUIResource) -> GameConstants.RarityLevel:
	if not is_instance_valid(ball_resource):
		return GameConstants.RarityLevel.COMMON
	return ball_resource.rarity_level

## 从遗物资源自动提取稀有度信息
## @param relic: Relic实例
## @return: 对应的稀有度等级
static func extract_relic_rarity(relic: Relic) -> GameConstants.RarityLevel:
	if not is_instance_valid(relic):
		return GameConstants.RarityLevel.COMMON
	return relic.rarity_level

## 从弹球实例自动添加权重配置
## @param ball: BallBase实例
func add_ball_weight_config(ball: BallBase, ball_scene: PackedScene) -> void:
	if not is_instance_valid(ball) or not is_instance_valid(ball.ui_resource):
		push_error("UpgradeOptionGenerator: 无效的弹球实例或缺少UI资源")
		return

	var ball_id = ball.ui_resource.ball_name
	var rarity = extract_ball_rarity(ball.ui_resource)
	var ui_data: BallUIData = UIDataConverter.ball_to_ui_data(ball)
	var weight = ball.ui_resource.rarity_weight

	set_item_weight_config(ball_id, rarity, weight, GameConstants.ItemType.BALL, ball_scene, ui_data)

## 从遗物实例自动添加权重配置
## @param relic: Relic实例
func add_relic_weight_config(relic: Relic) -> void:
	if not is_instance_valid(relic):
		push_error("UpgradeOptionGenerator: 无效的遗物实例")
		return

	var relic_id = relic.name
	var rarity = extract_relic_rarity(relic)
	var ui_data: RelicUIData = UIDataConverter.relic_to_ui_data(relic)
	var weight = relic.rarity_weight

	set_item_weight_config(relic_id, rarity, weight, GameConstants.ItemType.RELIC, relic, ui_data)


## 批量从遗物数组添加权重配置
## @param relics: Relic实例数组
func add_relics_weight_configs(relics: Array[Relic]) -> void:
	var added_count = 0
	for relic in relics:
		if is_instance_valid(relic):
			add_relic_weight_config(relic)
			added_count += 1

	print("UpgradeOptionGenerator: 批量添加遗物权重配置，成功添加 %d 个" % added_count)


## 生成升级选项
## @param option_count: 需要生成的选项数量
## @param current_balls: 当前拥有的弹球数据
## @param current_relics: 当前拥有的遗物数据
## @return: 生成的升级选项数组
func generate_upgrade_options(option_count: int = 3, current_balls: Array[BallUIData] = [], current_relics: Array[RelicUIData] = []) -> Array[UIItemDataBase]:
	var options: Array[UIItemDataBase] = []

	if _weight_configs.is_empty():
		push_warning("UpgradeOptionGenerator: 没有可用的权重配置")
		return options

	# 创建候选选项池
	var candidates: Array[ItemWeightConfig] = []
	for config in _weight_configs.values():
		if config.is_valid() and config.weight > 0.0:
			candidates.append(config)

	if candidates.is_empty():
		push_warning("UpgradeOptionGenerator: 没有有效的候选选项")
		return options

	# 根据权重随机选择选项
	var selected_configs: Array[ItemWeightConfig] = []
	for i in range(option_count):
		var selected_config = _select_weighted_random(candidates)
		if selected_config and selected_config.ui_data:
			selected_configs.append(selected_config)
			# 避免重复选择同一个选项
			candidates.erase(selected_config)
			if candidates.is_empty():
				break

	# 转换为UI数据
	for config in selected_configs:
		if config.ui_data:
			options.append(config.ui_data)

	print("UpgradeOptionGenerator: 成功生成 %d 个升级选项" % options.size())
	return options


## 根据权重随机选择一个配置
## @param candidates: 候选配置数组
## @return: 选中的配置，如果没有候选项返回null
func _select_weighted_random(candidates: Array[ItemWeightConfig]) -> ItemWeightConfig:
	if candidates.is_empty():
		return null

	# 计算总权重
	var total_weight: float = 0.0
	for config in candidates:
		total_weight += config.weight

	if total_weight <= 0.0:
		# 如果总权重为0，随机选择一个
		return candidates[randi() % candidates.size()]

	# 权重随机选择
	var random_value = randf() * total_weight
	var current_weight: float = 0.0

	for config in candidates:
		current_weight += config.weight
		if random_value <= current_weight:
			return config

	# 兜底返回最后一个
	return candidates[-1]

## 获取稀有度权重分布信息
## @return: 包含各稀有度权重分布的字典
func get_rarity_weight_distribution() -> Dictionary:
	var distribution = {}

	# 初始化各稀有度的权重统计
	for rarity in GameConstants.RarityLevel.values():
		distribution[rarity] = {
			"count": 0,
			"total_weight": 0.0,
			"average_weight": 0.0,
			"items": []
		}

	# 统计各稀有度的权重信息
	for config in _weight_configs.values():
		var rarity_data = distribution[config.rarity]
		rarity_data.count += 1
		rarity_data.total_weight += config.weight
		rarity_data.items.append(config.item_id)

	# 计算平均权重
	for rarity in GameConstants.RarityLevel.values():
		var rarity_data = distribution[rarity]
		if rarity_data.count > 0:
			rarity_data.average_weight = rarity_data.total_weight / rarity_data.count

	return distribution

## 打印权重配置统计信息
func print_weight_stats() -> void:
	var stats = get_weight_config_stats()
	var distribution = get_rarity_weight_distribution()

	print("=== 升级选项生成器权重配置统计 ===")
	print("总物品数量: %d" % stats.total_count)
	print("总权重: %.2f" % stats.total_weight)
	print("")

	print("按稀有度分布:")
	for rarity in GameConstants.RarityLevel.values():
		var rarity_name = GameConstants.get_rarity_display_name(rarity)
		var count = stats.rarity_counts[rarity]
		var rarity_data = distribution[rarity]
		print("  %s: %d 个物品, 总权重: %.2f, 平均权重: %.2f" % [rarity_name, count, rarity_data.total_weight, rarity_data.average_weight])

	print("")
	print("按类型分布:")
	for item_type in stats.type_counts.keys():
		var type_name = GameConstants.get_item_type_name(item_type)
		print("  %s: %d 个物品" % [type_name, stats.type_counts[item_type]])

	print("================================")
