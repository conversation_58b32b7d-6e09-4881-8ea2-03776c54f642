class_name UpgradeManager
extends Node

## 升级管理器
##
## 负责管理游戏的升级流程，包括：
## - 监听GameManager的level_up信号
## - 协调升级面板的显示和隐藏
## - 管理升级流程的状态
## - 生成升级选项并处理玩家选择
## - 协调升级效果的应用

## 升级面板引用（通过依赖注入设置）
var level_up_panel: LevelUpPanel = null

## 玩家管理器引用（通过依赖注入设置）
var player_manager: PlayerManager = null

## 遗物管理器引用（通过依赖注入设置）
var relic_manager: RelicManager = null

## 弹球池管理器引用（通过依赖注入设置）
var ball_pool_manager: BallPoolManager = null



## 升级选项生成器
var upgrade_option_generator: UpgradeOptionGenerator = null

## 升级流程状态标志
var is_upgrading: bool = false

## 当前升级选项数据
var current_upgrade_options: Array[UIItemDataBase] = []

var ball_scenes: Array[PackedScene] = []
var relic_resources: Array[Relic] = []

## 升级完成信号
signal upgrade_completed()

## 升级取消信号
signal upgrade_cancelled()


func _ready() -> void:
	# 启用输入处理用于调试功能
	set_process_input(true)


func _input(event: InputEvent) -> void:
	# 调试功能：按T键触发升级流程
	if event is InputEventKey and event.pressed and event.keycode == KEY_T:
		debug_trigger_upgrade()


## 初始化升级管理器
func _initialize() -> void:
	# 连接信号
	_connect_signals()

	# 初始化升级选项生成器
	_setup_upgrade_option_generator()

	print("UpgradeManager: 升级管理器初始化完成")


## 连接信号
func _connect_signals() -> void:
	# 连接GameManager的升级信号
	if GameManager:
		if not GameManager.level_up.is_connected(_on_level_up):
			GameManager.level_up.connect(_on_level_up)
			print("UpgradeManager: 已连接GameManager.level_up信号")
	else:
		push_error("UpgradeManager: GameManager不可用")
	
	# 连接升级面板信号
	if level_up_panel:
		if not level_up_panel.upgrade_selected.is_connected(_on_upgrade_selected):
			level_up_panel.upgrade_selected.connect(_on_upgrade_selected)
		if not level_up_panel.panel_closed.is_connected(_on_panel_closed):
			level_up_panel.panel_closed.connect(_on_panel_closed)
		print("UpgradeManager: 已连接升级面板信号")


## 设置升级选项生成器
func _setup_upgrade_option_generator() -> void:
	upgrade_option_generator = UpgradeOptionGenerator.new()
	for ball_scene in ball_scenes:
		var ball = ball_scene.instantiate()
		add_child(ball)
		upgrade_option_generator.add_ball_weight_config(ball, ball_scene)
		ball.queue_free()

	upgrade_option_generator.add_relics_weight_configs(relic_resources)
	print("UpgradeManager: 升级选项生成器初始化完成")


## 处理升级信号
func _on_level_up() -> void:
	if is_upgrading:
		push_warning("UpgradeManager: 升级流程已在进行中，忽略新的升级信号")
		return
	
	print("UpgradeManager: 收到升级信号，开始升级流程")
	_start_upgrade_process()


## 开始升级流程
func _start_upgrade_process() -> void:
	is_upgrading = true
	
	# 生成升级选项
	var upgrade_options = _generate_upgrade_options()
	if upgrade_options.is_empty():
		push_error("UpgradeManager: 无法生成升级选项")
		_end_upgrade_process()
		return
	
	current_upgrade_options = upgrade_options
	
	# 显示升级面板
	_show_upgrade_panel()


## 生成升级选项
func _generate_upgrade_options() -> Array[UIItemDataBase]:
	var options: Array[UIItemDataBase] = []

	if not upgrade_option_generator:
		push_error("UpgradeManager: 升级选项生成器不可用")
		return options

	# 获取当前游戏状态数据
	var _level_data = UIDataConverter.get_level_data()
	var current_balls = _get_current_balls_data()
	var current_relics = _get_current_relics_data()

	# 使用升级选项生成器生成真实的升级选项
	options = upgrade_option_generator.generate_upgrade_options(3, current_balls, current_relics)

	return options


## 获取当前弹球数据
func _get_current_balls_data() -> Array[BallUIData]:
	var balls_data: Array[BallUIData] = []

	if not ball_pool_manager:
		push_warning("UpgradeManager: 弹球池管理器不可用，无法获取弹球数据")
		return balls_data

	# 从弹球池管理器获取玩家的弹球数据
	var player_balls = ball_pool_manager.get_player_balls()
	for ball in player_balls:
		if is_instance_valid(ball) and ball is BallBase:
			var ui_data = _convert_ball_to_ui_data(ball as BallBase)
			if ui_data:
				balls_data.append(ui_data)

	print("UpgradeManager: 获取到 %d 个弹球数据" % balls_data.size())
	return balls_data


## 将弹球实例转换为UI数据
## @param ball: 弹球实例
## @return: 弹球UI数据
func _convert_ball_to_ui_data(ball: BallBase) -> BallUIData:
	if not is_instance_valid(ball):
		return null

	# 使用UIDataConverter进行转换
	var ui_data = UIDataConverter.ball_to_ui_data(ball)
	if ui_data:
		return ui_data

	# 如果UIDataConverter转换失败，使用备用方案
	var ball_name: String = "未知弹球"
	if ball.name and ball.name != "":
		ball_name = ball.name

	var ball_description = "基础弹球"
	var ball_icon = "⚪"
	var ball_type = "existing_ball"
	var ball_count = 1

	# 获取弹球的属性信息
	var attributes: Array[String] = []
	attributes.append("等级: %d" % ball.level)

	# 如果弹球有属性组件，获取其属性
	if ball.has_method("get_attribute_component"):
		var attr_component = ball.get_attribute_component()
		if attr_component and attr_component.has_method("get_damage"):
			var damage = attr_component.get_damage()
			attributes.append("伤害: %d" % damage)

	return BallUIData.new(ball_name, ball_description, ball_icon, ball_type, ball_count, attributes)


## 获取当前遗物数据
func _get_current_relics_data() -> Array[RelicUIData]:
	var relics_data: Array[RelicUIData] = []
	
	if not relic_manager:
		return relics_data
	
	# 从遗物管理器获取遗物数据并转换为UI数据
	for relic in relic_manager.relics:
		var ui_data = UIDataConverter.relic_to_ui_data(relic)
		if ui_data:
			relics_data.append(ui_data)
	
	return relics_data



## 显示升级面板
func _show_upgrade_panel() -> void:
	if not level_up_panel:
		push_warning("UpgradeManager: 升级面板不可用，跳过显示（可能在测试环境中）")
		# 在测试环境中，我们仍然可以继续升级流程
		print("UpgradeManager: 模拟升级面板显示，当前升级选项:")
		for i in range(current_upgrade_options.size()):
			var option = current_upgrade_options[i]
			print("  选项 %d: %s" % [i + 1, option.name])
		return

	# 获取等级数据
	var level_data = UIDataConverter.get_level_data()
	var balls_data = _get_current_balls_data()
	var relics_data = _get_current_relics_data()

	# 显示面板
	level_up_panel.show_panel(
		level_data,
		balls_data,
		relics_data,
		current_upgrade_options
	)

	print("UpgradeManager: 升级面板已显示")


## 处理升级选择
func _on_upgrade_selected(upgrade_data: UIItemDataBase) -> void:
	if not is_upgrading:
		push_warning("UpgradeManager: 收到升级选择但当前不在升级流程中")
		return
	
	print("UpgradeManager: 玩家选择了升级选项: %s" % upgrade_data.name)
	
	# 应用升级效果
	_apply_upgrade_effect(upgrade_data)
	
	# 结束升级流程
	_end_upgrade_process()
	
	# 发送升级完成信号
	upgrade_completed.emit()


## 应用升级效果
func _apply_upgrade_effect(upgrade_data: UIItemDataBase) -> void:
	if not upgrade_data:
		push_error("UpgradeManager: 升级数据为空")
		return

	print("UpgradeManager: 开始应用升级效果 - %s" % upgrade_data.name)

	var success = false
	var error_message = ""

	# 根据升级数据类型进行不同的处理
	if upgrade_data is BallUIData:
		var ball_data = upgrade_data as BallUIData
		success = _apply_ball_upgrade(ball_data)
		if not success:
			error_message = "弹球升级应用失败"
	elif upgrade_data is RelicUIData:
		var relic_data = upgrade_data as RelicUIData
		success = _apply_relic_upgrade(relic_data)
		if not success:
			error_message = "遗物升级应用失败"
	else:
		error_message = "未知的升级数据类型"
		push_error("UpgradeManager: %s" % error_message)

	# 输出结果
	if success:
		print("UpgradeManager: 升级效果应用成功 - %s" % upgrade_data.name)
	else:
		push_error("UpgradeManager: 升级效果应用失败 - %s: %s" % [upgrade_data.name, error_message])


## 应用弹球升级效果
## @param ball_data: 弹球升级数据
## @return: 是否应用成功
func _apply_ball_upgrade(ball_data: BallUIData) -> bool:
	if not ball_pool_manager:
		push_error("UpgradeManager: 弹球池管理器不可用")
		return false

	match ball_data.ball_type:
		"new_ball":
			return _add_new_ball(ball_data)
		"upgrade_ball":
			return _upgrade_existing_ball(ball_data)
		_:
			push_error("UpgradeManager: 未知的弹球升级类型 - %s" % ball_data.ball_type)
			return false


## 添加新弹球
## @param ball_data: 弹球数据
## @return: 是否添加成功
func _add_new_ball(ball_data: BallUIData) -> bool:
	print("UpgradeManager: 添加新弹球 - %s" % ball_data.name)

	# 根据弹球名称获取对应的弹球场景资源
	var ball_scene = _get_ball_scene_by_name(ball_data.name)
	if not ball_scene:
		push_error("UpgradeManager: 无法找到弹球场景资源 - %s" % ball_data.name)
		return false

	# 使用弹球池管理器添加新弹球
	var new_ball = ball_pool_manager.add_player_ball(ball_scene)
	if new_ball:
		print("UpgradeManager: 成功添加新弹球 - %s" % ball_data.name)
		return true
	else:
		push_error("UpgradeManager: 弹球池管理器添加弹球失败")
		return false


## 升级现有弹球
## @param ball_data: 弹球数据
## @return: 是否升级成功
func _upgrade_existing_ball(ball_data: BallUIData) -> bool:
	print("UpgradeManager: 升级现有弹球 - %s" % ball_data.name)

	# 获取玩家当前的弹球列表
	var player_balls = ball_pool_manager.get_player_balls()
	if player_balls.is_empty():
		push_error("UpgradeManager: 玩家没有可升级的弹球")
		return false

	# 选择第一个弹球进行升级
	var target_ball = player_balls[0]
	if _upgrade_ball_attributes(target_ball, ball_data):
		print("UpgradeManager: 成功升级弹球 - %s" % target_ball.name)
		return true
	else:
		push_error("UpgradeManager: 弹球属性升级失败")
		return false


## 升级弹球属性
## @param ball: 目标弹球
## @param upgrade_data: 升级数据
## @return: 是否升级成功
func _upgrade_ball_attributes(ball: BallBase, upgrade_data: BallUIData) -> bool:
	if not is_instance_valid(ball):
		return false

	# 提升弹球等级
	ball.level += 1
	print("UpgradeManager: 弹球等级提升至 %d" % ball.level)

	# 如果弹球有属性组件，提升其属性
	if ball.has_method("get_attribute_component"):
		var attr_component = ball.get_attribute_component()
		if attr_component and attr_component.has_method("get_damage"):
			var current_damage = attr_component.get_damage()
			var new_damage = current_damage * 1.2  # 提升20%伤害
			if attr_component.has_method("set_damage"):
				attr_component.set_damage(new_damage)
				print("UpgradeManager: 弹球伤害从 %d 提升至 %d" % [current_damage, new_damage])

	return true


## 应用遗物升级效果
## @param relic_data: 遗物升级数据
## @return: 是否应用成功
func _apply_relic_upgrade(relic_data: RelicUIData) -> bool:
	if not relic_manager:
		push_error("UpgradeManager: 遗物管理器不可用")
		return false

	print("UpgradeManager: 添加新遗物 - %s" % relic_data.name)

	# 根据遗物名称获取对应的遗物资源
	var relic_resource = _get_relic_resource_by_name(relic_data.name)
	if not relic_resource:
		push_error("UpgradeManager: 无法找到遗物资源 - %s" % relic_data.name)
		return false

	# 使用遗物管理器添加新遗物
	relic_manager.add_relic(relic_resource)
	print("UpgradeManager: 成功添加新遗物 - %s" % relic_data.name)

	return true


## 根据弹球名称获取弹球场景资源
## @param ball_name: 弹球名称
## @return: 弹球场景资源
func _get_ball_scene_by_name(ball_name: String) -> PackedScene:
	# 从注入的弹球场景列表中查找匹配的弹球
	for ball_scene in ball_scenes:
		if not ball_scene:
			continue

		# 实例化弹球来检查名称
		var ball_instance = ball_scene.instantiate()
		if is_instance_valid(ball_instance) and ball_instance.has_method("get_ui_resource"):
			var ui_resource = ball_instance.get_ui_resource()
			if ui_resource and ui_resource.ball_name == ball_name:
				ball_instance.queue_free()
				return ball_scene

		ball_instance.queue_free()

	# 如果没有找到匹配的弹球，使用默认映射
	match ball_name:
		"雷电弹球", "尖刺弹球":
			return preload("res://ball/prefab/shock_ball.tscn")
		"火焰", "火焰弹球":
			return preload("res://ball/prefab/fire_ball.tscn")
		"剧毒弹球":
			return preload("res://ball/prefab/poison_ball.tscn")
		"地震":
			return preload("res://ball/prefab/shock_ball.tscn")  # 使用现有的弹球场景
		"幽灵":
			return preload("res://ball/prefab/ghost_ball.tscn")
		_:
			push_warning("UpgradeManager: 未知的弹球名称，使用默认弹球 - %s" % ball_name)
			return preload("res://ball/prefab/shock_ball.tscn")


## 根据遗物名称获取遗物资源
## @param relic_name: 遗物名称
## @return: 遗物资源
func _get_relic_resource_by_name(relic_name: String) -> Resource:
	# 从注入的遗物资源列表中查找匹配的遗物
	for relic_resource in relic_resources:
		if is_instance_valid(relic_resource) and relic_resource.name == relic_name:
			return relic_resource

	# 如果没有找到匹配的遗物，使用默认映射
	match relic_name:
		"幸运硬币", "铁甲":
			return preload("res://relic/relics/bottom_crit.tres")
		_:
			push_warning("UpgradeManager: 未知的遗物名称，使用默认遗物 - %s" % relic_name)
			return preload("res://relic/relics/bottom_crit.tres")


## 处理面板关闭
func _on_panel_closed() -> void:
	if is_upgrading:
		print("UpgradeManager: 升级面板被关闭，取消升级流程")
		_end_upgrade_process()
		upgrade_cancelled.emit()


## 结束升级流程
func _end_upgrade_process() -> void:
	is_upgrading = false
	current_upgrade_options.clear()
	print("UpgradeManager: 升级流程结束")


## 设置依赖（依赖注入接口）
## @param p_level_up_panel: 升级面板引用
## @param p_player_manager: 玩家管理器引用
## @param p_ball_pool_manager: 弹球池管理器引用
## @param p_relic_manager: 遗物管理器引用
## @param p_ball_scenes: 弹球场景数组
## @param p_relic_resources: 遗物资源数组
func set_dependencies(p_level_up_panel: LevelUpPanel, p_player_manager: PlayerManager, \
 	p_ball_pool_manager: BallPoolManager, p_relic_manager: RelicManager, \
  	p_ball_scenes: Array[PackedScene], p_relic_resources: Array[Relic]) -> void:
	level_up_panel = p_level_up_panel
	player_manager = p_player_manager
	ball_pool_manager = p_ball_pool_manager
	relic_manager = p_relic_manager
	ball_scenes = p_ball_scenes
	relic_resources = p_relic_resources

	print("UpgradeManager: 依赖已注入，正在初始化")
	_initialize()


## 调试功能：触发升级流程
func debug_trigger_upgrade() -> void:
	print("UpgradeManager: [调试] 按T键触发升级流程")
	_on_level_up()
