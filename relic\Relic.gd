class_name Relic extends Resource

## 遗物名称
@export var name: String
## 效果描述
@export_multiline var description: String
## 图标
@export var icon: Texture2D
## 遗物效果逻辑
@export var effect: RelicEffect

## 遗物稀有度（使用全局枚举）
@export var rarity_level: GameConstants.RarityLevel = GameConstants.RarityLevel.COMMON

## 遗物权重（用于升级选项生成）
@export var rarity_weight: float = 8.0


## 获取遗物的 AttributeSet（统一接口）
## @return: 遗物的 AttributeSet 实例
func get_attribute_set() -> AttributeSet:
	if is_instance_valid(effect):
		return effect.get_attribute_set()
	return null

## 获取稀有度对应的颜色
func get_rarity_color() -> Color:
	return GameConstants.get_rarity_color(rarity_level)

## 获取稀有度显示名称
func get_rarity_display_name() -> String:
	return GameConstants.get_rarity_display_name(rarity_level)

## 获取显示名称（包含稀有度信息）
func get_display_name() -> String:
	var rarity_name = get_rarity_display_name()
	if rarity_level != GameConstants.RarityLevel.COMMON:
		return "%s (%s)" % [name, rarity_name]
	else:
		return name

## 获取权重值（确保非负）
func get_weight() -> float:
	return max(0.0, rarity_weight)

## 设置权重值（确保非负）
func set_weight(new_weight: float) -> void:
	rarity_weight = max(0.0, new_weight)

## 检查遗物数据是否有效
func is_valid() -> bool:
	return name != "" and icon != null and is_instance_valid(effect)
