[gd_scene load_steps=4 format=3 uid="uid://bq8xvn2h3k8ys"]

[ext_resource type="Shader" uid="uid://dlcncsnxplcg2" path="res://assets/materials/shader/light_sweep.gdshader" id="1_fqfc8"]
[ext_resource type="Script" uid="uid://ckywi7y6sn1tm" path="res://ui/prefab/item_slot.gd" id="2_script"]

[sub_resource type="ShaderMaterial" id="ShaderMaterial_fqfc8"]
shader = ExtResource("1_fqfc8")
shader_parameter/sweep_speed = 7.978
shader_parameter/intensity = 1.0
shader_parameter/brightness = 0.777
shader_parameter/sweep_color = Color(1, 1, 1, 1)
shader_parameter/direction = Vector2(-0.5, 2)
shader_parameter/sweep_interval = 10.261
shader_parameter/sweep_width = 5.0
shader_parameter/edge_sharpness = 1.719
shader_parameter/sweep_visible = true

[node name="ItemSlot" type="Button"]
custom_minimum_size = Vector2(60, 90)
offset_right = 60.0
offset_bottom = 90.0
size_flags_horizontal = 6
size_flags_vertical = 4
theme_type_variation = &"IconSlot"
action_mode = 0
script = ExtResource("2_script")

[node name="VBoxContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_constants/separation = 3
alignment = 1

[node name="TextureRect" type="TextureRect" parent="VBoxContainer"]
custom_minimum_size = Vector2(55, 55)
layout_mode = 2
size_flags_horizontal = 4
size_flags_vertical = 10
mouse_filter = 2
expand_mode = 1
stretch_mode = 5

[node name="LevelLabel" type="Label" parent="VBoxContainer"]
custom_minimum_size = Vector2(0, 22)
layout_mode = 2
size_flags_horizontal = 4
theme_override_font_sizes/font_size = 18
text = "1"
horizontal_alignment = 1

[node name="SweepOverlay" type="ColorRect" parent="."]
material = SubResource("ShaderMaterial_fqfc8")
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
color = Color(1, 1, 1, 0)

[connection signal="pressed" from="." to="." method="_on_slot_pressed"]
